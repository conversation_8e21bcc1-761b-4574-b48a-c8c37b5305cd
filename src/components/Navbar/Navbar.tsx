'use client';
import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { AnimatePresence, motion } from 'framer-motion';

// Import extracted modules
import { Icon } from '@/components/icons';
import type { NavItem, NavDropdownItem } from './types';
import { NAV_ITEMS } from './config/navigationItems';
import { useNavbar } from './hooks/useNavbar';
import { NavLogo, HamburgerMenu, CTAButtons, MegaMenu } from './components';

// Mobile menu component - Side Panel
const MobileMenu = React.memo(function MobileMenu({
  isOpen,
  activeDropdown,
  setActiveDropdown,
  onClose
}: {
  isOpen: boolean;
  activeDropdown: string | null;
  setActiveDropdown: (dropdown: string | null) => void;
  onClose: () => void;
}) {
  const pathname = usePathname();

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop Overlay */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
            onClick={onClose}
            data-testid="mobile-overlay"
          />

          {/* Side Panel */}
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className="fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-white shadow-2xl z-50 lg:hidden overflow-y-auto"
            data-testid="mobile-menu"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-white">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                  <img
                    src="/images/logo.png"
                    alt="Mocky Digital"
                    className="w-6 h-6 rounded-md"
                  />
                </div>
                <h2 className="text-lg font-semibold text-gray-900">Menu</h2>
              </div>
              <button
                onClick={onClose}
                className="p-2 rounded-lg bg-white/10 hover:bg-white/20 text-white transition-colors"
                aria-label="Close menu"
              >
                <Icon name="x" className="w-5 h-5" />
              </button>
            </div>

            {/* Menu Content */}
            <div className="p-6 space-y-2">
          {NAV_ITEMS.map((item) => (
            <div key={item.label}>
              {item.dropdown ? (
                <div>
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      console.log('Dropdown clicked:', item.label, 'Current active:', activeDropdown);
                      setActiveDropdown(activeDropdown === item.label ? null : item.label);
                    }}
                    className={`flex items-center justify-between w-full py-3 px-4 text-left font-medium transition-all duration-200 rounded-lg ${
                      activeDropdown === item.label
                        ? 'text-orange-600 bg-gradient-to-r from-orange-50 to-orange-100 shadow-sm'
                        : 'text-gray-900 bg-gray-50 hover:bg-gray-100'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <Icon name={item.icon || 'briefcase'} className="w-5 h-5 text-gray-600" />
                      <span>{item.label}</span>
                    </div>
                    <Icon 
                      name="chevronDown" 
                      className={`w-4 h-4 text-gray-600 transition-transform ${
                        activeDropdown === item.label ? 'rotate-180' : ''
                      }`} 
                    />
                  </button>
                  
                  <AnimatePresence>
                    {activeDropdown === item.label && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.2 }}
                        className="mt-2 ml-4 space-y-2"
                      >
                        {item.dropdown.map((dropdownItem) => {
                          if ('isGroup' in dropdownItem && dropdownItem.isGroup) {
                            return (
                              <div key={dropdownItem.label} className="py-2">
                                <h4 className="text-sm font-semibold text-gray-700 px-3 py-2 bg-gradient-to-r from-gray-100 to-gray-50 rounded-lg mb-2">
                                  {dropdownItem.label}
                                </h4>
                                <div className="ml-2 space-y-1">
                                  {dropdownItem.items?.map((service) => (
                                    <Link
                                      key={service.href}
                                      href={service.href || '#'}
                                      onClick={onClose}
                                      className="flex items-center space-x-3 py-3 px-3 text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-all duration-200"
                                    >
                                      <div className="w-2 h-2 bg-orange-400 rounded-full opacity-60"></div>
                                      <Icon name={service.icon || 'star'} className="w-4 h-4" />
                                      <span className="font-medium">{service.label}</span>
                                    </Link>
                                  ))}
                                </div>
                              </div>
                            );
                          } else {
                            const nonGroupItem = dropdownItem as NavDropdownItem;
                            return (
                              <Link
                                key={nonGroupItem.href}
                                href={nonGroupItem.href || '#'}
                                onClick={onClose}
                                className="flex items-center space-x-3 py-3 px-4 text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-all duration-200 font-medium"
                              >
                                <Icon name={nonGroupItem.icon || 'grid'} className="w-5 h-5" />
                                <span>{nonGroupItem.label}</span>
                              </Link>
                            );
                          }
                        })}
                      </motion.div>
                    )}
                      </AnimatePresence>
                    </div>
                  ) : (
                    <Link
                      href={item.href || '#'}
                      onClick={onClose}
                      className={`flex items-center space-x-3 py-4 px-4 rounded-xl font-medium transition-all duration-200 ${
                        pathname === item.href
                          ? 'text-orange-600 bg-gradient-to-r from-orange-50 to-orange-100 shadow-sm'
                          : 'text-gray-900 hover:text-orange-600 hover:bg-gray-50'
                      }`}
                    >
                      <div className={`p-2 rounded-lg ${
                        pathname === item.href
                          ? 'bg-orange-200 text-orange-700'
                          : 'bg-gray-100 text-gray-600'
                      }`}>
                        <Icon name={item.icon || 'home'} className="w-4 h-4" />
                      </div>
                      <span className="font-medium">{item.label}</span>
                      {item.badge && (
                        <span className="ml-auto px-2 py-1 text-xs font-bold text-white bg-red-500 rounded-full">
                          {item.badge}
                        </span>
                      )}
                    </Link>
                  )}
                </div>
              ))}

              {/* CTA Section */}
              <div className="mt-8 pt-6 border-t border-gray-200">
                <div className="space-y-3">
                  <CTAButtons isMobile={true} />
                </div>
              </div>

              {/* Footer */}
              <div className="mt-8 pt-6 border-t border-gray-200 text-center">
                <p className="text-sm text-gray-500">
                  © 2024 Mocky Digital
                </p>
                <p className="text-xs text-gray-400 mt-1">
                  Professional Design & Digital Services
                </p>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
});

MobileMenu.displayName = 'MobileMenu';

// Main Navbar Component
export default function Navbar() {
  const { 
    isOpen, 
    activeDropdown, 
    scrolled, 
    mounted, 
    headerRef,
    setIsOpen, 
    setActiveDropdown 
  } = useNavbar();

  const pathname = usePathname();

  // Loading state for SSR
  if (!mounted) {
    return (
      <header className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200">
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gray-200 rounded-lg animate-pulse" />
              <div className="hidden sm:block space-y-1">
                <div className="h-4 w-32 bg-gray-200 rounded animate-pulse" />
                <div className="h-3 w-24 bg-gray-200 rounded animate-pulse" />
              </div>
            </div>
            <div className="hidden lg:flex items-center space-x-1">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="h-8 w-16 bg-gray-200 rounded animate-pulse" />
              ))}
            </div>
            <div className="w-10 h-10 bg-gray-200 rounded-lg animate-pulse lg:hidden" />
          </div>
        </div>
      </header>
    );
  }

  return (
    <>
      <header
        ref={headerRef}
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
          scrolled 
            ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200' 
            : 'bg-white/80 backdrop-blur-sm border-b border-gray-100'
        }`}
      >
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="flex justify-between items-center h-20">
            <NavLogo />

            <nav className="hidden lg:flex items-center space-x-1">
              {NAV_ITEMS.map((item) => (
                <div key={item.label} className="relative dropdown-container">
                  {item.dropdown ? (
                    <>
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setActiveDropdown(activeDropdown === item.label ? null : item.label);
                        }}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            setActiveDropdown(activeDropdown === item.label ? null : item.label);
                          }
                          if (e.key === 'Escape') {
                            setActiveDropdown(null);
                          }
                        }}
                        tabIndex={0}
                        data-dropdown-toggle="true"
                        className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 ${
                          activeDropdown === item.label
                            ? 'text-orange-600 bg-orange-50'
                            : 'text-gray-700 hover:text-orange-600 hover:bg-gray-50'
                        }`}
                        aria-expanded={activeDropdown === item.label}
                        aria-controls={`${item.label}-dropdown`}
                      >
                        <Icon name={item.icon || 'briefcase'} className="w-4 h-4" />
                        <span>{item.label}</span>
                        <Icon 
                          name="chevronDown" 
                          className={`w-3 h-3 transition-transform duration-200 ${
                            activeDropdown === item.label ? 'rotate-180' : ''
                          }`} 
                        />
                      </button>
                      {activeDropdown === item.label && (
                        <MegaMenu 
                          item={item} 
                          isOpen={activeDropdown === item.label} 
                          onClose={() => setActiveDropdown(null)} 
                        />
                      )}
                    </>
                  ) : (
                    <Link
                      href={item.href || '#'}
                      className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 relative ${
                        pathname === item.href
                          ? 'text-orange-600 bg-orange-50'
                          : 'text-gray-700 hover:text-orange-600 hover:bg-gray-50'
                      }`}
                    >
                      <Icon name={item.icon || 'home'} className="w-4 h-4" />
                      <span>{item.label}</span>
                      {item.badge && (
                        <span className="absolute -top-2 -right-1 px-2 py-1 text-xs font-bold text-white bg-red-500 rounded-full">
                          {item.badge}
                        </span>
                      )}
                    </Link>
                  )}
                </div>
              ))}
            </nav>

            <div className="flex items-center space-x-4">
              <div className="hidden lg:block">
                <CTAButtons />
              </div>
              <HamburgerMenu 
                isOpen={isOpen} 
                onToggle={() => setIsOpen(!isOpen)} 
              />
            </div>
          </div>
        </div>
      </header>

      <MobileMenu 
        isOpen={isOpen} 
        activeDropdown={activeDropdown}
        setActiveDropdown={setActiveDropdown}
        onClose={() => setIsOpen(false)} 
      />
    </>
  );
}
