'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import ClientCallbackForm from '@/components/ClientCallbackForm';
import PortfolioSlider from '@/components/PortfolioSlider';
import ServicesSlider from '@/components/ServicesSlider';
import WebDevelopmentPortfolioSection from '@/components/WebDevelopmentPortfolioSection';
import Testimonials from '@/components/Testimonials';
import WebDevelopmentPortfolio from '@/components/WebDevelopmentPortfolio';
import { SparklesIcon, ArrowRightIcon, ArrowDownIcon } from '@heroicons/react/24/outline';
import { motion, HTMLMotionProps } from 'framer-motion';

// Motion component variants
const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  whileInView: { opacity: 1, y: 0 },
  transition: { duration: 0.6 },
  viewport: { once: true }
};

const fadeInUpDelay = (delay: number) => ({
  initial: { opacity: 0, y: 20 },
  whileInView: { opacity: 1, y: 0 },
  transition: { duration: 0.6, delay },
  viewport: { once: true }
});

// Create motion components with proper types
type MotionDivProps = HTMLMotionProps<"div">;
type MotionH2Props = HTMLMotionProps<"h2">;
type MotionPProps = HTMLMotionProps<"p">;

const MotionDiv = (props: MotionDivProps) => <motion.div {...props} />;
const MotionH2 = (props: MotionH2Props) => <motion.h2 {...props} />;
const MotionP = (props: MotionPProps) => <motion.p {...props} />;

interface HomePageClientProps {
  settings: any;
}

export default function HomePageClient({ settings }: HomePageClientProps) {
  const [mounted, setMounted] = useState<boolean>(false);
  const [activeFaq, setActiveFaq] = useState<number | null>(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  const toggleFaq = (index: number) => {
    setActiveFaq(activeFaq === index ? null : index);
  };

  if (!mounted) {
    return null;
  }

  // WhatsApp link precomputed
  const whatsappLink = "https://wa.me/254741590670?text=Hello%20Mocky%20Digital!";

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-50 via-white via-gray-50/30 to-orange-50/20 pt-16">
      {/* Revolutionary Hero Section */}
      <div className="relative min-h-[60vh] sm:min-h-[70vh] lg:min-h-[80vh] bg-gradient-to-b from-slate-50 via-white to-gray-50/30 overflow-hidden flex items-center">
        {/* Dynamic Background Pattern */}
        <div className="absolute inset-0 opacity-[0.03]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="homepage-grid" width="15" height="15" patternUnits="userSpaceOnUse">
                <path d="M 15 0 L 0 0 0 15" fill="none" stroke="#FF5400" strokeWidth="0.3"/>
                <circle cx="7.5" cy="7.5" r="0.5" fill="#FF5400" opacity="0.1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#homepage-grid)" />
          </svg>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 right-10 w-32 h-32 bg-gradient-to-br from-blue-200/40 to-purple-200/40 rounded-full blur-2xl"></div>
        <div className="absolute bottom-20 left-10 w-40 h-40 bg-gradient-to-br from-orange-200/40 to-red-200/40 rounded-full blur-3xl"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20">
          <div className="text-center space-y-8 sm:space-y-10 lg:space-y-12">
            {/* Premium Badge with Animation */}
            <div className="inline-flex items-center gap-2 sm:gap-3 bg-white/90 backdrop-blur-xl text-[#FF5400] px-4 sm:px-6 lg:px-8 py-3 sm:py-4 rounded-full text-xs sm:text-sm font-bold border border-orange-200/60 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:scale-105 group">
              <SparklesIcon className="h-4 w-4 sm:h-5 sm:w-5 group-hover:rotate-12 transition-transform duration-300" />
              <span className="bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                WELCOME TO MOCKY DIGITAL
              </span>
            </div>

            {/* Revolutionary Title */}
            <div className="space-y-4 sm:space-y-6">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black text-gray-900 leading-[0.9] tracking-tight px-2">
                Transform Your{' '}
                <span className="relative inline-block">
                  <span className="bg-gradient-to-r from-[#FF5400] via-orange-500 to-red-500 bg-clip-text text-transparent">
                    Digital
                  </span>
                  <div className="absolute -bottom-1 sm:-bottom-2 left-0 right-0 h-0.5 sm:h-1 bg-gradient-to-r from-orange-400 to-red-400 rounded-full opacity-30"></div>
                </span>
              </h1>
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black text-gray-800 leading-tight px-2">
                Presence & Brand
              </h2>
            </div>

            <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-light px-4">
              From creative design to digital marketing, we build 
              <span className="font-semibold text-gray-800"> comprehensive solutions that drive growth</span>
            </p>

            {/* CTA Buttons with Enhanced Styling */}
            <div className="flex flex-col sm:flex-row gap-5 justify-center">
              <a
                href={whatsappLink}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 bg-[#FF5400] hover:bg-[#FF5400]/90 text-white px-8 py-4 rounded-full font-medium text-center transition-all duration-500 hover:scale-105 shadow-xl hover:shadow-2xl group"
              >
                <span>Get Started</span>
                <ArrowRightIcon className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </a>
              <Link
                href="#services"
                className="inline-flex items-center gap-2 border-2 border-gray-200 hover:border-[#FF5400]/30 text-gray-800 px-8 py-4 rounded-full font-medium text-center transition-all duration-500 hover:scale-105 group"
              >
                <span>Our Services</span>
                <ArrowDownIcon className="h-5 w-5 group-hover:translate-y-1 transition-transform" />
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Services Overview */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden" id="services">
        {/* Background decoration */}
        <div className="absolute top-0 right-0 w-96 h-96 bg-[#FF5400]/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-[#0A1929]/5 rounded-full blur-3xl"></div>
        
        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center mb-24">
            {/* Section Tag */}
            <MotionDiv {...fadeInUp} className="flex items-center justify-center gap-3 mb-8">
              <div className="w-8 h-[2px] bg-[#FF5400]"></div>
              <span className="text-[#FF5400] uppercase tracking-wider font-semibold text-sm">
                Our Services
              </span>
              <div className="w-8 h-[2px] bg-[#FF5400]"></div>
            </MotionDiv>

            {/* Main Heading */}
            <MotionH2 {...fadeInUpDelay(0.1)} className="text-4xl md:text-5xl lg:text-6xl font-bold text-[#0A1929] mb-8 leading-tight">
              What We
              <span className="block text-[#FF5400] relative">
                Offer
                <span className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-[#FF5400]/20 rounded-full"></span>
              </span>
            </MotionH2>

            {/* Description */}
            <MotionP {...fadeInUpDelay(0.2)} className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-light mb-4">
              Comprehensive creative and digital solutions designed to elevate your brand and drive meaningful results.
            </MotionP>

            <MotionP {...fadeInUpDelay(0.3)} className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed">
              Our expertise spans across strategic design, innovative development, and data-driven marketing to transform your vision into reality.
            </MotionP>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {[
              {
                icon: 'fas fa-palette',
                title: 'Creative Design',
                description: 'Professional graphic design services including logos, branding, and marketing materials.',
                color: 'from-[#0A1929] to-[#1a2332]',
                bgColor: 'bg-blue-50',
                features: ['Logo Design', 'Brand Identity', 'Print Design']
              },
              {
                icon: 'fas fa-globe',
                title: 'Web Development',
                description: 'Modern, responsive websites and web applications built with the latest technologies.',
                color: 'from-[#FF5400] to-[#FF7A00]',
                bgColor: 'bg-green-50',
                features: ['Responsive Design', 'E-commerce', 'Web Apps']
              },
              {
                icon: 'fas fa-bullhorn',
                title: 'Digital Marketing',
                description: 'Strategic digital marketing campaigns to boost your online presence and drive growth.',
                color: 'from-[#0A1929] to-[#1a2332]',
                bgColor: 'bg-purple-50',
                features: ['Social Media', 'SEO', 'Paid Ads']
              },
              {
                icon: 'fas fa-print',
                title: 'Branding & Print',
                description: 'Complete branding solutions including merchandise, signage, and promotional materials.',
                color: 'from-[#FF5400] to-[#FF7A00]',
                bgColor: 'bg-orange-50',
                features: ['Brand Strategy', 'Merchandise', 'Signage']
              },
              {
                icon: 'fas fa-chart-line',
                title: 'Business Consulting',
                description: 'Expert guidance on branding strategy, market positioning, and business growth.',
                color: 'from-[#0A1929] to-[#1a2332]',
                bgColor: 'bg-indigo-50',
                features: ['Strategy', 'Market Research', 'Growth Planning']
              },
              {
                icon: 'fas fa-cogs',
                title: 'IT Solutions',
                description: 'Comprehensive IT services including system integration, cloud solutions, and support.',
                color: 'from-[#FF5400] to-[#FF7A00]',
                bgColor: 'bg-red-50',
                features: ['Cloud Setup', 'System Integration', 'IT Support']
              }
            ].map((service, index) => (
              <MotionDiv
                key={index}
                {...fadeInUpDelay(0.1 + index * 0.1)}
                className="group relative"
              >
                <div className="bg-white rounded-3xl p-8 border border-gray-100 hover:border-[#FF5400]/30 hover:shadow-2xl transition-all duration-500 h-full relative overflow-hidden">
                  {/* Gradient background on hover */}
                  <div className="absolute inset-0 bg-gradient-to-br from-[#FF5400]/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
                  
                  {/* Icon with gradient background */}
                  <div className="relative mb-6">
                    <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${service.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                      <i className={`${service.icon} text-white text-lg`}></i>
                    </div>
                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-[#FF5400] rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 animate-pulse"></div>
                  </div>
                  
                  <div className="relative">
                    <h3 className="text-xl font-bold text-[#0A1929] mb-4 group-hover:text-[#FF5400] transition-colors duration-300">
                      {service.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed mb-6">
                      {service.description}
                    </p>
                    
                    {/* Feature tags */}
                    <div className="flex flex-wrap gap-2">
                      {service.features.map((feature, idx) => (
                        <span 
                          key={idx}
                          className="px-3 py-1 text-xs font-medium bg-gray-100 text-gray-700 rounded-full group-hover:bg-[#FF5400]/10 group-hover:text-[#FF5400] transition-all duration-300"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  {/* Hover arrow */}
                  <div className="absolute bottom-6 right-6 w-8 h-8 bg-[#FF5400] rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transform translate-x-4 group-hover:translate-x-0 transition-all duration-300">
                    <i className="fas fa-arrow-right text-white text-sm"></i>
                  </div>
                </div>
              </MotionDiv>
            ))}
          </div>


        </div>
      </section>

      {/* About Us & Callback Form Section */}
      <section className="py-24 bg-white">
        <div className="container mx-auto px-6">
          <div className="grid lg:grid-cols-2 gap-20 items-start">
            {/* About Us Content - Left Side */}
            <div className="space-y-10">
              <div className="space-y-8">
                {/* Section Tag */}
                <div className="flex items-center gap-3">
                  <div className="w-8 h-[2px] bg-[#FF5400]"></div>
                  <span className="text-[#FF5400] uppercase tracking-wider font-semibold text-sm">
                    About Mocky Digital
                  </span>
                </div>

                {/* Main Heading */}
                <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-[#0A1929] leading-tight">
                  Crafting Digital
                  <span className="block text-[#FF5400] relative">
                    Excellence
                    <span className="absolute -bottom-2 left-0 w-full h-1 bg-[#FF5400]/20 rounded-full"></span>
                  </span>
                </h2>

                {/* Content */}
                <div className="space-y-8">
                  <p className="text-xl text-gray-600 leading-relaxed font-light">
                    We're a passionate team of creators who believe in the power of exceptional design and innovative technology to transform businesses.
                  </p>

                  <p className="text-lg text-gray-700 leading-relaxed">
                    Based in Nairobi, our diverse team combines strategic thinking, creative excellence, and cutting-edge technology to deliver impactful solutions for organizations across Africa and beyond.
                  </p>

                  {/* Stats or highlights */}
                  <div className="grid grid-cols-2 gap-6 pt-6">
                    <div className="text-center lg:text-left">
                      <div className="text-3xl font-bold text-[#FF5400]">100+</div>
                      <div className="text-sm text-gray-600 uppercase tracking-wide">Projects Delivered</div>
                    </div>
                    <div className="text-center lg:text-left">
                      <div className="text-3xl font-bold text-[#FF5400]">5+</div>
                      <div className="text-sm text-gray-600 uppercase tracking-wide">Years Experience</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Callback Form - Right Side */}
            <div className="bg-gradient-to-br from-gray-50 to-white rounded-3xl shadow-xl border border-gray-100 p-10">
              <div className="mb-10">
                {/* Form Tag */}
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-6 h-[2px] bg-[#FF5400]"></div>
                  <span className="text-[#FF5400] uppercase tracking-wider font-semibold text-xs">
                    Get In Touch
                  </span>
                </div>

                {/* Form Heading */}
                <h3 className="text-3xl md:text-4xl font-bold text-[#0A1929] mb-4 leading-tight">
                  Start Your
                  <span className="block text-[#FF5400]">Project Today</span>
                </h3>

                <p className="text-gray-600 text-lg leading-relaxed">
                  Ready to bring your vision to life? Fill out the form below and we'll get back to you within 24 hours.
                </p>
              </div>

              <ClientCallbackForm />
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-gradient-to-b from-white to-gray-50 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-[0.03]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="faq-grid" width="15" height="15" patternUnits="userSpaceOnUse">
                <path d="M 15 0 L 0 0 0 15" fill="none" stroke="#FF5400" strokeWidth="0.3"/>
                <circle cx="7.5" cy="7.5" r="0.5" fill="#FF5400" opacity="0.1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#faq-grid)" />
          </svg>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 right-10 w-32 h-32 bg-gradient-to-br from-blue-200/40 to-indigo-200/40 rounded-full blur-2xl"></div>
        <div className="absolute bottom-20 left-10 w-40 h-40 bg-gradient-to-br from-orange-200/40 to-red-200/40 rounded-full blur-3xl"></div>

        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#FF5400]/10 border border-[#FF5400]/20 mb-6">
              <i className="fas fa-question-circle text-[#FF5400]"></i>
              <span className="text-sm font-medium text-[#FF5400]">FAQ</span>
            </div>

            <h2 className="text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
              Frequently Asked <span className="text-[#FF5400]">Questions</span>
            </h2>

            <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Common questions about our services and how we work
            </p>
          </div>

          <div className="max-w-7xl mx-auto">
            <div className="grid md:grid-cols-2 gap-6">
              {[
                {
                  question: "What services do you offer?",
                  answer: "We offer comprehensive creative and digital services including graphic design, web development, digital marketing, branding, print design, and business consulting. Our goal is to be your one-stop solution for all digital and creative needs."
                },
                {
                  question: "How long does a typical project take?",
                  answer: "Project timelines vary based on scope and complexity. Logo design typically takes 3-5 days, websites 1-4 weeks, and comprehensive branding projects 2-6 weeks. We'll provide a detailed timeline during our initial consultation."
                },
                {
                  question: "Do you work with businesses of all sizes?",
                  answer: "Absolutely! We work with startups, small businesses, medium enterprises, and large corporations. Our services are scalable and can be tailored to meet the specific needs and budget of any business size."
                },
                {
                  question: "What makes Mocky Digital different?",
                  answer: "Our unique combination of creative expertise, technical skills, and strategic thinking sets us apart. We don't just create beautiful designs - we create solutions that drive business growth and deliver measurable results."
                },
                {
                  question: "Do you provide ongoing support after project completion?",
                  answer: "Yes, we offer various support packages including website maintenance, content updates, social media management, and technical support. We believe in building long-term partnerships with our clients."
                },
                {
                  question: "Can you help with both online and offline marketing?",
                  answer: "Definitely! We provide comprehensive marketing solutions including digital marketing (social media, SEO, online ads) and traditional marketing (print materials, signage, merchandise). We create cohesive campaigns across all channels."
                },
                {
                  question: "What is your design process?",
                  answer: "Our process includes: 1) Discovery & consultation, 2) Research & strategy development, 3) Creative concept development, 4) Design execution, 5) Client review & refinement, 6) Final delivery & support. We keep you involved throughout the entire process."
                },
                {
                  question: "Do you offer custom packages?",
                  answer: "Yes, we understand that every business has unique needs. We offer flexible, custom packages tailored to your specific requirements, budget, and goals. Contact us to discuss a personalized solution for your business."
                }
              ].map((faq, index) => (
                <div
                  key={index}
                  className="group bg-white rounded-3xl p-6 border border-gray-100 hover:border-[#FF5400]/30 hover:shadow-xl transition-all duration-500"
                >
                  <button
                    onClick={() => toggleFaq(index)}
                    className="w-full text-left"
                  >
                    <div className="flex items-start justify-between">
                      <h3 className="text-lg font-semibold text-gray-900 pr-4">{faq.question}</h3>
                      <i className={`fas fa-chevron-${activeFaq === index ? 'up' : 'down'} text-[#FF5400] transition-transform duration-300 flex-shrink-0 mt-1`}></i>
                    </div>
                    {activeFaq === index && (
                      <div className="mt-4 pt-4 border-t border-gray-100">
                        <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                      </div>
                    )}
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Portfolio Slider Section */}
      <PortfolioSlider />

      {/* Testimonials Section */}
      <Testimonials />



      {/* Social Media Section - Using data from the database */}
      <section className="py-20 bg-gray-50" id="connect">
        <div className="container mx-auto px-6">
          <div className="max-w-xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">
              Connect With Us
            </h2>
            <p className="text-lg text-gray-600 mb-10">
              Follow us on social media for updates and inspiration
            </p>

            <div className="flex flex-col gap-4 max-w-md mx-auto">
              {/* Facebook - Use database setting or fallback to default */}
              <a
                href={settings?.facebookUrl || "https://facebook.com/mockydigital"}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-[#1877F2] hover:bg-[#166FE5] text-white flex items-center justify-center gap-3 py-3 px-6 rounded-full font-medium transition-all duration-300"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="h-5 w-5 fill-current">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                </svg>
                <span>Follow on Facebook</span>
              </a>

              {/* Instagram - Use database setting or fallback to default */}
              <a
                href={settings?.instagramUrl || "https://instagram.com/mockydigital"}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-gradient-to-r from-[#833AB4] via-[#FD1D1D] to-[#F77737] hover:opacity-90 text-white flex items-center justify-center gap-3 py-3 px-6 rounded-full font-medium transition-all duration-300"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="h-5 w-5 fill-current">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z" />
                </svg>
                <span>Follow on Instagram</span>
              </a>

              {/* X (Twitter) - Use database setting or fallback to default */}
              <a
                href={settings?.twitterUrl || "https://twitter.com/mockydigital"}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-black hover:bg-gray-900 text-white flex items-center justify-center gap-3 py-3 px-6 rounded-full font-medium transition-all duration-300"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="h-5 w-5 fill-current">
                  <path d="M18.901 1.153h3.68l-8.04 9.19L24 22.846h-7.406l-5.8-7.584-6.638 7.584H.474l8.6-9.83L0 1.154h7.594l5.243 6.932ZM17.61 20.644h2.039L6.486 3.24H4.298Z" />
                </svg>
                <span>Follow on X</span>
              </a>

              {/* TikTok - Use database setting or fallback to default */}
              <a
                href={settings?.tiktokUrl || "https://www.tiktok.com/@mockydigital"}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-black hover:bg-gray-900 text-white flex items-center justify-center gap-3 py-3 px-6 rounded-full font-medium transition-all duration-300"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" className="h-5 w-5 fill-current">
                  <path d="M448 209.9a210.1 210.1 0 0 1 -122.8-39.3V349.4A162.6 162.6 0 1 1 185 188.3V278.2a74.6 74.6 0 1 0 52.2 71.2V0l88 0a121.2 121.2 0 0 0 1.9 22.2h0A122.2 122.2 0 0 0 381 102.4a121.4 121.4 0 0 0 67 20.1z" />
                </svg>
                <span>Follow on TikTok</span>
              </a>

              {/* LinkedIn - Only show if URL exists in settings */}
              {settings?.linkedinUrl && (
                <a
                  href={settings.linkedinUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-[#0077B5] hover:bg-[#006699] text-white flex items-center justify-center gap-3 py-3 px-6 rounded-full font-medium transition-all duration-300"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="h-5 w-5 fill-current">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                  </svg>
                  <span>Follow on LinkedIn</span>
                </a>
              )}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
