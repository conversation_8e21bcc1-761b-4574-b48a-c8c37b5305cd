'use client';

import { useState, useEffect } from 'react';
import { SparklesIcon } from '@heroicons/react/24/outline';
import ModernFAQSection from '@/components/ModernFAQSection';

// Digital marketing services data
const digitalMarketingServices = [
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
      </svg>
    ),
    title: 'Search Engine Optimization',
    description: 'Improve your website\'s visibility on search engines and drive organic traffic to your business.',
    features: [
      'Keyword Research & Strategy',
      'On-Page SEO Optimization',
      'Technical SEO Audits',
      'Local SEO for Businesses'
    ]
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>
    ),
    title: 'Social Media Marketing',
    description: 'Build your brand presence across social platforms and engage with your target audience effectively.',
    features: [
      'Content Strategy & Planning',
      'Social Media Management',
      'Community Engagement',
      'Influencer Partnerships'
    ]
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
      </svg>
    ),
    title: 'Pay-Per-Click Advertising',
    description: 'Drive immediate traffic and conversions with targeted Google Ads and social media advertising campaigns.',
    features: [
      'Google Ads Campaigns',
      'Facebook & Instagram Ads',
      'Landing Page Optimization',
      'Conversion Tracking'
    ]
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
      </svg>
    ),
    title: 'Email Marketing',
    description: 'Build lasting relationships with your customers through strategic email campaigns and automation.',
    features: [
      'Email Campaign Design',
      'Marketing Automation',
      'List Building Strategies',
      'Performance Analytics'
    ]
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
      </svg>
    ),
    title: 'Content Marketing',
    description: 'Create compelling content that attracts, engages, and converts your target audience into loyal customers.',
    features: [
      'Content Strategy Development',
      'Blog Writing & SEO',
      'Video Content Creation',
      'Content Distribution'
    ]
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>
    ),
    title: 'Analytics & Reporting',
    description: 'Track, measure, and optimize your digital marketing performance with comprehensive analytics and insights.',
    features: [
      'Google Analytics Setup',
      'Performance Dashboards',
      'ROI Measurement',
      'Monthly Reporting'
    ]
  }
];



// FAQ data
const faqData = [
  {
    question: "How long does it take to see results from digital marketing?",
    answer: "Results vary by channel and strategy. PPC ads can show immediate results, while SEO typically takes 3-6 months. Social media and content marketing usually show significant results within 2-4 months of consistent effort."
  },
  {
    question: "What's included in your digital marketing packages?",
    answer: "Our packages include strategy development, campaign setup and management, content creation, performance tracking, and monthly reporting. Specific services vary by package level and can be customized to your needs."
  },
  {
    question: "Do you work with businesses of all sizes?",
    answer: "Yes, we work with startups, small businesses, and large enterprises. Our strategies and packages are scalable and can be customized to fit your budget and business goals."
  },
  {
    question: "How do you measure the success of digital marketing campaigns?",
    answer: "We track key performance indicators (KPIs) including website traffic, conversion rates, cost per acquisition, return on ad spend (ROAS), and overall ROI. We provide detailed monthly reports with actionable insights."
  },
  {
    question: "Can you help with both organic and paid marketing strategies?",
    answer: "Absolutely! We offer comprehensive services including organic strategies like SEO and content marketing, as well as paid advertising through Google Ads, social media ads, and other platforms for maximum reach and impact."
  }
];

export default function DigitalMarketingPage() {
  const [mounted, setMounted] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    services: '',
    message: ''
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Create WhatsApp message
    const whatsappMessage = `Hello Mocky Digital! I'd like to schedule a coffee meeting to discuss digital marketing services.

*Contact Details:*
Name: ${formData.name}
Email: ${formData.email}
Phone: ${formData.phone}
Company: ${formData.company || 'Not specified'}

*Service Interest:* ${formData.services || 'Not specified'}

*Message:*
${formData.message || 'No additional message'}

Looking forward to our coffee chat!`;

    const whatsappUrl = `https://wa.me/254741590670?text=${encodeURIComponent(whatsappMessage)}`;
    window.open(whatsappUrl, '_blank');
  };



  if (!mounted) {
    return null;
  }

  return (
    <main className="min-h-screen bg-gradient-to-b from-slate-50 via-white via-gray-50/30 to-orange-50/20 pt-16">
      {/* Modern Hero Section - Matching Catalogue Page Design */}
      <div className="relative min-h-[60vh] sm:min-h-[70vh] lg:min-h-[80vh] bg-gradient-to-b from-slate-50 via-white to-gray-50/30 overflow-hidden flex items-center">
        {/* Dynamic Background Pattern */}
        <div className="absolute inset-0 opacity-[0.03]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="digital-marketing-grid" width="15" height="15" patternUnits="userSpaceOnUse">
                <path d="M 15 0 L 0 0 0 15" fill="none" stroke="#FF5400" strokeWidth="0.3"/>
                <circle cx="7.5" cy="7.5" r="0.5" fill="#FF5400" opacity="0.1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#digital-marketing-grid)" />
          </svg>
        </div>

        {/* Enhanced Floating Elements - Responsive */}
        <div className="absolute top-10 sm:top-20 left-4 sm:left-10 w-20 sm:w-32 lg:w-40 h-20 sm:h-32 lg:h-40 bg-gradient-to-br from-orange-200/40 to-red-200/40 rounded-full blur-xl sm:blur-2xl animate-pulse"></div>
        <div className="absolute top-16 sm:top-32 right-4 sm:right-16 w-16 sm:w-24 lg:w-32 h-16 sm:h-24 lg:h-32 bg-gradient-to-br from-blue-200/30 to-purple-200/30 rounded-full blur-lg sm:blur-xl animate-pulse" style={{ animationDelay: '1.5s' }}></div>
        <div className="absolute bottom-20 sm:bottom-40 left-1/4 w-14 sm:w-20 lg:w-28 h-14 sm:h-20 lg:h-28 bg-gradient-to-br from-green-200/25 to-teal-200/25 rounded-full blur-lg sm:blur-xl animate-pulse" style={{ animationDelay: '2.5s' }}></div>
        <div className="absolute bottom-12 sm:bottom-24 right-1/4 sm:right-1/3 w-18 sm:w-28 lg:w-36 h-18 sm:h-28 lg:h-36 bg-gradient-to-br from-pink-200/30 to-rose-200/30 rounded-full blur-xl sm:blur-2xl animate-pulse" style={{ animationDelay: '0.8s' }}></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20">
          <div className="text-center space-y-8 sm:space-y-10 lg:space-y-12">
            {/* Premium Badge with Animation */}
            <div className="inline-flex items-center gap-2 sm:gap-3 bg-white/90 backdrop-blur-xl text-[#FF5400] px-4 sm:px-6 lg:px-8 py-3 sm:py-4 rounded-full text-xs sm:text-sm font-bold border border-orange-200/60 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:scale-105 group">
              <SparklesIcon className="h-4 w-4 sm:h-5 sm:w-5 group-hover:rotate-12 transition-transform duration-300" />
              <span className="bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                DIGITAL MARKETING SOLUTIONS
              </span>
            </div>

            {/* Revolutionary Title */}
            <div className="space-y-4 sm:space-y-6">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black text-gray-900 leading-[0.9] tracking-tight px-2">
                Grow Your{' '}
                <span className="relative inline-block">
                  <span className="bg-gradient-to-r from-[#FF5400] via-orange-500 to-red-500 bg-clip-text text-transparent">
                    Business
                  </span>
                  <div className="absolute -bottom-1 sm:-bottom-2 left-0 right-0 h-0.5 sm:h-1 bg-gradient-to-r from-orange-400 to-red-400 rounded-full opacity-30"></div>
                </span>
              </h1>
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black text-gray-800 leading-tight px-2">
                Online Today
              </h2>
            </div>

            <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-light px-4">
              Transform your digital presence with data-driven marketing strategies that deliver measurable results.
              From SEO to social media, we help you <span className="font-semibold text-gray-800">reach your target audience and achieve sustainable growth</span>
            </p>
          </div>
        </div>
      </div>

      {/* Value Proposition Section - Modern Design */}
      <section className="py-20 bg-gradient-to-br from-white to-gray-50/30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-3 mb-8">
              <div className="w-8 h-[2px] bg-[#FF5400]"></div>
              <span className="text-[#FF5400] uppercase tracking-wider font-semibold text-sm">
                Why Digital Marketing
              </span>
              <div className="w-8 h-[2px] bg-[#FF5400]"></div>
            </div>

            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-[#0A1929] mb-6 leading-tight">
              Transform Your
              <span className="block text-[#FF5400] relative">
                Online Presence
                <span className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-[#FF5400]/20 rounded-full"></span>
              </span>
            </h2>

            <p className="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed">
              In today's digital world, your online presence determines your business success.
              We help you leverage the power of digital marketing to reach more customers, increase sales, and grow your brand.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: (
                  <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                ),
                title: 'Increased Visibility',
                description: 'Get found by your target audience when they\'re searching for your products or services'
              },
              {
                icon: (
                  <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                ),
                title: 'Targeted Reach',
                description: 'Reach the right people at the right time with precision-targeted marketing campaigns'
              },
              {
                icon: (
                  <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                ),
                title: 'Measurable Results',
                description: 'Track every click, conversion, and sale with detailed analytics and performance reports'
              },
              {
                icon: (
                  <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                ),
                title: 'Cost-Effective',
                description: 'Get better ROI compared to traditional marketing with optimized digital strategies'
              }
            ].map((benefit, index) => (
              <div
                key={index}
                className="text-center p-6 rounded-xl hover:shadow-lg transition-shadow"
              >
                <div className="flex justify-center mb-4 text-[#FF5400]">{benefit.icon}</div>
                <h3 className="text-xl font-bold mb-3 text-gray-900">{benefit.title}</h3>
                <p className="text-gray-600">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section - Modern Design */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-3 mb-8">
              <div className="w-8 h-[2px] bg-[#FF5400]"></div>
              <span className="text-[#FF5400] uppercase tracking-wider font-semibold text-sm">
                Our Services
              </span>
              <div className="w-8 h-[2px] bg-[#FF5400]"></div>
            </div>

            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-[#0A1929] mb-6 leading-tight">
              Comprehensive
              <span className="block text-[#FF5400] relative">
                Digital Marketing
                <span className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-28 h-1 bg-[#FF5400]/20 rounded-full"></span>
              </span>
            </h2>

            <p className="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed">
              From strategy to execution, we provide end-to-end digital marketing services that drive real business results.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {digitalMarketingServices.map((service, index) => (
              <div
                key={index}
                className="bg-white p-8 rounded-xl shadow-sm hover:shadow-lg transition-all duration-300"
              >
                <div className="flex justify-center mb-6 text-[#FF5400]">{service.icon}</div>
                <h3 className="text-xl font-bold mb-4 text-gray-900">{service.title}</h3>
                <p className="text-gray-600 mb-6">{service.description}</p>
                <ul className="space-y-2">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center gap-3">
                      <div className="w-2 h-2 rounded-full bg-[#FF5400]"></div>
                      <span className="text-gray-600 text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Digital Marketing Impact Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 via-white to-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left Content */}
            <div className="lg:pr-8">
              <span className="inline-block px-4 py-1.5 bg-[#FF5400] text-white text-sm font-medium rounded-full mb-4 shadow-sm">
                DIGITAL IMPACT
              </span>
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gray-900 leading-tight">
                The Power of Digital Marketing
              </h2>
              <p className="text-lg text-gray-600 mb-10 leading-relaxed">
                Digital marketing isn't just a trend—it's the future of business growth. 
                With the right strategy, you can reach global audiences, build meaningful relationships, and drive sustainable growth.
              </p>

              {/* Statistics */}
              <div className="space-y-8">
                {[
                  {
                    stat: '4.8B',
                    description: 'people use the internet worldwide, creating massive opportunities for businesses',
                    icon: (
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                      </svg>
                    )
                  },
                  {
                    stat: '89%',
                    description: 'of consumers research products online before making purchase decisions',
                    icon: (
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    )
                  },
                  {
                    stat: '200%',
                    description: 'average ROI that businesses see from effective email marketing campaigns',
                    icon: (
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                      </svg>
                    )
                  }
                ].map((stat, index) => (
                  <div key={index} className="flex items-start gap-6 p-6 bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-[#FF5400]/10 rounded-lg flex items-center justify-center text-[#FF5400]">
                        {stat.icon}
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="text-3xl font-bold text-[#FF5400] mb-2">{stat.stat}</div>
                      <div className="text-gray-600 leading-relaxed">{stat.description}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Right Visual Elements */}
            <div className="relative lg:pl-8">
              <div className="relative">
                {/* Background decorative elements */}
                <div className="absolute -top-4 -left-4 w-24 h-24 bg-[#FF5400]/10 rounded-full blur-xl"></div>
                <div className="absolute -bottom-8 -right-8 w-32 h-32 bg-[#0A2647]/10 rounded-full blur-xl"></div>
                
                {/* Main grid */}
                <div className="grid grid-cols-2 gap-6 relative z-10">
                  {[
                    { 
                      title: 'SEO', 
                      color: 'bg-[#FF5400]',
                      textColor: 'text-white',
                      description: 'Organic Traffic',
                      icon: (
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                      )
                    },
                    { 
                      title: 'Social Media', 
                      color: 'bg-[#0A2647]',
                      textColor: 'text-white',
                      highlightColor: 'text-[#FF5400]',
                      description: 'Brand Engagement',
                      icon: (
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                      )
                    },
                    { 
                      title: 'PPC Ads', 
                      color: 'bg-gray-800',
                      textColor: 'text-white',
                      highlightColor: 'text-[#FF5400]',
                      description: 'Paid Traffic',
                      icon: (
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
                        </svg>
                      )
                    },
                    { 
                      title: 'Analytics', 
                      color: 'bg-gradient-to-br from-[#FF5400] to-[#e84a00]',
                      textColor: 'text-white',
                      description: 'Data Insights',
                      icon: (
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                      )
                    }
                  ].map((element, index) => (
                    <div
                      key={index}
                      className={`${element.color} ${element.textColor} p-8 rounded-2xl hover:scale-105 transition-all duration-300 cursor-pointer shadow-lg hover:shadow-xl`}
                    >
                      <div className="flex flex-col items-center text-center space-y-3">
                        <div className="text-white">{element.icon}</div>
                        <div>
                          <h3 className={`font-bold text-lg mb-1 ${element.highlightColor || 'text-white'}`}>{element.title}</h3>
                          <p className="text-sm text-white/90">{element.description}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Coffee Meeting Form Section */}
      <section className="py-20 bg-gradient-to-br from-white to-gray-50/30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <div className="flex items-center justify-center gap-3 mb-8">
                <div className="w-8 h-[2px] bg-[#FF5400]"></div>
                <span className="text-[#FF5400] uppercase tracking-wider font-semibold text-sm">
                  Let's Connect
                </span>
                <div className="w-8 h-[2px] bg-[#FF5400]"></div>
              </div>

              <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-[#0A1929] mb-6 leading-tight">
                Let's Grab Coffee
                <span className="block text-[#FF5400] relative">
                  Sometime & Discuss More
                  <span className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-[#FF5400]/20 rounded-full"></span>
                </span>
              </h2>

              <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed mb-12">
                Ready to transform your digital presence? Let's meet over coffee and discuss how we can help your business grow online.
                Share your details below and we'll reach out to schedule a personalized consultation.
              </p>
            </div>

            <div className="bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden">
              <div className="p-8 sm:p-12">
                <form onSubmit={handleSubmit} className="space-y-8">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-semibold text-gray-700 mb-3">
                        Full Name *
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#FF5400] focus:border-[#FF5400] transition-colors"
                        placeholder="Your full name"
                      />
                    </div>
                    <div>
                      <label htmlFor="email" className="block text-sm font-semibold text-gray-700 mb-3">
                        Email Address *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#FF5400] focus:border-[#FF5400] transition-colors"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="phone" className="block text-sm font-semibold text-gray-700 mb-3">
                        Phone Number *
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#FF5400] focus:border-[#FF5400] transition-colors"
                        placeholder="+*********** 000"
                      />
                    </div>
                    <div>
                      <label htmlFor="company" className="block text-sm font-semibold text-gray-700 mb-3">
                        Company Name
                      </label>
                      <input
                        type="text"
                        id="company"
                        name="company"
                        value={formData.company}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#FF5400] focus:border-[#FF5400] transition-colors"
                        placeholder="Your company name"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="services" className="block text-sm font-semibold text-gray-700 mb-3">
                      Services of Interest
                    </label>
                    <select
                      id="services"
                      name="services"
                      value={formData.services}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#FF5400] focus:border-[#FF5400] transition-colors"
                    >
                      <option value="">Select a service</option>
                      <option value="Search Engine Optimization (SEO)">Search Engine Optimization (SEO)</option>
                      <option value="Social Media Marketing">Social Media Marketing</option>
                      <option value="Pay-Per-Click Advertising">Pay-Per-Click Advertising</option>
                      <option value="Email Marketing">Email Marketing</option>
                      <option value="Content Marketing">Content Marketing</option>
                      <option value="Analytics & Reporting">Analytics & Reporting</option>
                      <option value="All Services">All Services</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-semibold text-gray-700 mb-3">
                      Tell us about your project
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      rows={5}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#FF5400] focus:border-[#FF5400] transition-colors resize-none"
                      placeholder="Share your goals, challenges, and what you'd like to discuss over coffee..."
                    ></textarea>
                  </div>

                  <div className="text-center">
                    <button
                      type="submit"
                      className="inline-flex items-center gap-3 bg-[#FF5400] hover:bg-[#e84a00] text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                      Let's Schedule Our Coffee Chat
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-[#0A2647]">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Grow Your Business Online?
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Let's create a digital marketing strategy that drives real results for your business. 
            Contact us today for a free consultation.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => {
                const whatsappMessage = "Hello Mocky Digital! I'm interested in your digital marketing services. Please provide more information.";
                const whatsappUrl = `https://wa.me/254741590670?text=${encodeURIComponent(whatsappMessage)}`;
                window.open(whatsappUrl, '_blank');
              }}
              className="bg-[#FF5400] hover:bg-[#e84a00] text-white px-8 py-4 rounded-lg font-semibold transition-colors inline-flex items-center justify-center gap-3"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.306"/>
              </svg>
              Start Your Digital Journey
            </button>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <ModernFAQSection
        faqs={faqData}
      />
    </main>
  );
} 